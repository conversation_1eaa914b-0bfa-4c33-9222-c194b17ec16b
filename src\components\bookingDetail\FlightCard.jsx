"use client";
import React, { useState } from 'react';
import { Star, ArrowLeft, ArrowRight, Plane, Clock, Users, CreditCard, MapPin, Zap, X, StarIcon, PlaneTakeoff, PlaneTakeoffIcon, PlaneLanding } from 'lucide-react';
import Image from 'next/image';

const FlightCard = ({
  flightData = {},
  onPrevious,
  onNext,
  className = ""
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const {
    images = ["/assets/img/flight/single-1.jpg"],
    image = "/assets/img/flight/single-1.jpg",
    route = "New York - Los Angeles",
    type = "One Way",
    rating = 4.5,
    reviews = 35,
    takeOffTime = "Sat, 25 Oct | 07:30AM",
    takeOffCode = "JFK",
    landingTime = "Sat, 25 Oct | 09:25AM",
    landingCode = "LAX",
    stops = "1 Stop (STN)",
    stopDuration = "1h 25m",
    airline = "Delta Airlines",
    flightType = "Economy",
    fareType = "Refundable",
    cancellationFee = "$50 / Per Person",
    flightChange = "$50 / Per Person",
    seatsSequence = "$50 Extra Charge",
    inflightFeatures = "Available",
    taxesFees = "$50"
  } = flightData;

  // Use images array if available, otherwise fallback to single image
  const imageList = images.length > 0 ? images : [image];
  const currentImage = imageList[currentImageIndex];

  const handlePrevious = () => {
    if (imageList.length > 1) {
      setCurrentImageIndex((prev) => (prev === 0 ? imageList.length - 1 : prev - 1));
    }
    if (onPrevious) onPrevious();
  };

  const handleNext = () => {
    if (imageList.length > 1) {
      setCurrentImageIndex((prev) => (prev === imageList.length - 1 ? 0 : prev + 1));
    }
    if (onNext) onNext();
  };

  const features = [
    { icon: Plane, label: "Airline", value: airline },
    { icon: Clock, label: "Flight Type", value: flightType },
    { icon: MapPin, label: "Fare Type", value: fareType },
    { icon: X, label: "Cancellation Fee", value: cancellationFee },
    { icon: CreditCard, label: "Flight Change", value: flightChange },
    { icon: Users, label: "Seats & Baggage", value: seatsSequence },
    { icon: Zap, label: "Inflight Features", value: inflightFeatures },
    { icon: CreditCard, label: "Taxes & Fees", value: taxesFees }
  ];

  return (
    <div className={`overflow-hidden ${className}`}>
      {/* Flight Image with Navigation */}
      <div className="relative">
        <div className="aspect-video relative">
          <Image
            src={currentImage}
            alt={route}
            fill
            className="object-cover"
          />

          {/* Navigation Arrows - Show only if multiple images */}
          {imageList.length > 1 && (
            <>
              <button
                onClick={handlePrevious}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/80 hover:bg-white rounded-full flex items-center justify-center transition-colors"
              >
                <ArrowLeft size={20} className="text-gray-700" />
              </button>

              <button
                onClick={handleNext}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/80 hover:bg-white rounded-full flex items-center justify-center transition-colors"
              >
                <ArrowRight size={20} className="text-gray-700" />
              </button>
            </>
          )}

          {/* Image Indicators - Show only if multiple images */}
          {imageList.length > 1 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {imageList.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Flight Info */}
      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 gap-3 sm:gap-0">
  {/* Left Side */}
  <div>
    <h2 className="text-xl font-bold text-[#0C2C7A] mb-1">{route}</h2>

    {/* Stops Info */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
      <span className="text-sm text-gray-600">{type}</span>
      <div className="bg-[#24BDC7] text-white opacity-80 rounded-lg px-2 py-1 flex items-center space-x-2 w-fit">
        <span className="text-sm font-medium">{stops}</span>
        <span className="text-sm">{stopDuration}</span>
      </div>
    </div>
  </div>

  {/* Right Side */}
  <div className="flex items-center space-x-2">
    <span className="flex gap-1 bg-yellow-400 text-white px-2 rounded-full rounded-bl-none text-sm font-medium items-center">
      <StarIcon size={16} className="text-white fill-current" />
      {rating}
    </span>
    <span className="text-sm text-[#24BDC7]">Excellent</span>
    <span className="text-sm text-[#0C2C7A]">({reviews} Reviews)</span>
  </div>
</div>

        <hr className='border-gray-200 py-2 px-5' />

        {/* Flight Times */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 items-center">
          {/* Take Off */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10  flex items-center justify-center">
              <PlaneTakeoff size={30} className="text-[#0C2C7A]" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Flight Take Off</p>
              <p className="text-sm text-[#0C2C7A] font-semibold">{takeOffTime}</p>
              <p className="text-xs text-gray-500">{takeOffCode}</p>
            </div>
          </div>

          <div className="flex flex-col items-center">
            {/* Top Text (Stops Info) */}
            <span className="text-sm font-medium text-[#0C2C7A]">{stops}</span>

            {/* Line with Dot and Arrow */}
            <div className="flex items-center w-full justify-center relative mt-1 mb-1">
              {/* Horizontal Line */}
              <div className="w-48 h-[2px] bg-[#0C2C7A]"></div>

              {/* Dot in Center */}
              <div className="absolute left-1/2 -translate-x-1/2 w-3 h-3 bg-[#0C2C7A] rounded-full"></div>

            </div>

            {/* Bottom Text (Stop Duration) */}
            <span className="text-sm text-[#0C2C7A]">{stopDuration}</span>
          </div>

          {/* Landing */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 flex items-center justify-center">
              <PlaneLanding size={30} className="text-[#0C2C7A]" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Flight Landing</p>
              <p className="text-sm text-[#0C2C7A] font-semibold">{landingTime}</p>
              <p className="text-xs text-gray-500">{landingCode}</p>
            </div>
          </div>
        </div>
        <hr className='border-gray-200 py-2 px-5' />


        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <div key={index} className="text-center flex flex-row gap-4">
                <div className="w-10 h-10 bg-[#24BDC7] rounded-full rounded-bl-none flex items-center justify-center mb-2">
                  <IconComponent size={16} className="text-white" />
                </div>
                <div className='flex-col text-left'>
                <p className="text-xs font-medium text-gray-700 mb-1">{feature.label}</p>
                <p className="text-xs text-[#0C2C7A]">{feature.value}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FlightCard;
